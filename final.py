import pandas as pd

def process_files():
    # 读取第一个文件 file_gpt.xlsx
    df_gpt = pd.read_excel('file_gpt.xlsx', header=1)  # 从第二行开始读取数据
    
    # 初始化结果DataFrame
    matched_df = pd.DataFrame()  # 匹配的数据存入a.xlsx
    unmatched_df = pd.DataFrame()  # 不匹配的数据存入b.xlsx
    
    # 处理第二个文件 file_nas.xlsx
    df_nas = pd.read_excel('file_nas.xlsx', header=1)
    nas_matched = set()  # 记录已匹配的file_nas行
    
    # 处理第三个文件 file_ys.xlsx
    df_ys = pd.read_excel('file_ys.xlsx', header=1)
    ys_matched = set()  # 记录已匹配的file_ys行
    
    # 处理file_nas匹配逻辑
    for _, nas_row in df_nas.iterrows():
        nas_col1_value = nas_row.iloc[0]  # 第一列数据
        nas_col1_values = str(nas_col1_value).split('&') if '&' in str(nas_col1_value) else [str(nas_col1_value)]
        nas_col1_values = [v.strip() for v in nas_col1_values]
        
        matched = False
        for idx, gpt_row in df_gpt.iterrows():
            gpt_col6_value = gpt_row.iloc[5]  # 第六列数据
            gpt_col6_values = str(gpt_col6_value).split('/') if '/' in str(gpt_col6_value) else [str(gpt_col6_value)]
            gpt_col6_values = [v.strip() for v in gpt_col6_values]
            
            # 检查是否有交集
            if set(nas_col1_values) & set(gpt_col6_values):
                matched = True
                nas_matched.add(_)
                # 合并数据
                new_row = gpt_row.to_dict()
                new_row.update({
                    'NAS_Col2': nas_row.iloc[0],
                    'NAS_Col3': nas_row.iloc[2],
                    'Source': 'file_nas'
                })
                matched_df = pd.concat([matched_df, pd.DataFrame([new_row])], ignore_index=True)
        
        if not matched:
            new_unmatched_row = {
                '产品型号': nas_row.iloc[0],
                '地址': nas_row.iloc[1],
                '来源': 'file_nas_unmatched'
            }
            unmatched_df = pd.concat([unmatched_df, pd.DataFrame([new_unmatched_row])], ignore_index=True)
    
    # 处理file_ys匹配逻辑
    for _, ys_row in df_ys.iterrows():
        ys_col3_value = str(ys_row.iloc[2]).strip()  # 第三列数据
        
        matched = False
        for idx, gpt_row in df_gpt.iterrows():
            gpt_col6_value = gpt_row.iloc[5]  # 第六列数据
            gpt_col6_values = str(gpt_col6_value).split('/') if '/' in str(gpt_col6_value) else [str(gpt_col6_value)]
            gpt_col6_values = [v.strip() for v in gpt_col6_values]
            
            if ys_col3_value in gpt_col6_values:
                matched = True
                ys_matched.add(_)
                # 合并数据
                new_row = gpt_row.to_dict()
                new_row.update({
                    'YS_Col3': ys_row.iloc[2],
                    'YS_Col11': ys_row.iloc[10],
                    'Source': 'file_ys'
                })
                matched_df = pd.concat([matched_df, pd.DataFrame([new_row])], ignore_index=True)
        
        if not matched:
            new_unmatched_row = {
                '产品型号': ys_row.iloc[2],
                '地址': ys_row.iloc[10],
                '来源': 'file_ys_unmatched'
            }
            unmatched_df = pd.concat([unmatched_df, pd.DataFrame([new_unmatched_row])], ignore_index=True)
    
    # 保存结果到不同文件
    matched_df.to_excel('a.xlsx', index=False)
    unmatched_df.to_excel('b.xlsx', index=False)
    print("处理完成：")
    print(f"- 匹配数据已保存到 a.xlsx (共 {len(matched_df)} 条记录)")
    print(f"- 不匹配数据已保存到 b.xlsx (共 {len(unmatched_df)} 条记录)")

if __name__ == "__main__":
    process_files()