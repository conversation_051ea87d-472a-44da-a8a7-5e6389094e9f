import os
from openpyxl import Workbook
from openpyxl.styles import Font

def should_exclude_file(filename):
    """检查是否应该排除该文件（jpg/png）"""
    lower_filename = filename.lower()
    return lower_filename.endswith('.jpg') or lower_filename.endswith('.png') or lower_filename.endswith('.db')

def get_file_structure(rootdir):
    """
    获取文件结构
    返回格式: [(最后一层文件夹名, 文件名, 完整路径), ...]
    """
    file_structure = []
    
    for dirpath, _, filenames in os.walk(rootdir):
        # 过滤掉图片文件
        filtered_files = [f for f in filenames if not should_exclude_file(f)]
        
        # 只处理包含(非图片)文件的文件夹
        if filtered_files:
            # 获取最后一层文件夹名
            last_folder = os.path.basename(dirpath)
            
            # 获取相对于根目录的路径
            relative_path = os.path.relpath(dirpath, rootdir)
            if relative_path == '.':
                full_path = os.path.basename(rootdir)
            else:
                full_path = os.path.join(os.path.basename(rootdir), relative_path)
            
            # 添加每个文件到结果中
            for file in filtered_files:
                file_structure.append((
                    last_folder,
                    file,
                    os.path.join(full_path, file)
                ))
    
    return file_structure

def write_to_excel(data, output_file):
    """
    将文件结构写入Excel文件
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "文件结构"
    
    # 设置标题行
    headers = ["最后一层文件夹", "文件名", "完整路径"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header).font = Font(bold=True)
    
    # 写入数据
    for row_idx, (folder, filename, fullpath) in enumerate(data, 2):
        ws.cell(row=row_idx, column=1, value=folder)
        ws.cell(row=row_idx, column=2, value=filename)
        ws.cell(row=row_idx, column=3, value=fullpath)
    
    # 调整列宽
    ws.column_dimensions['A'].width = 30
    ws.column_dimensions['B'].width = 30
    ws.column_dimensions['C'].width = 60
    
    wb.save(output_file)
    print(f"文件结构已成功写入到 {output_file}")

if __name__ == "__main__":
    # 设置要扫描的文件夹路径
    folder_path = "./AI客服知识库"
    
    # 验证路径是否存在
    while not os.path.isdir(folder_path):
        print("错误: 指定的路径不存在或不是一个文件夹")
        folder_path = input("请重新输入要扫描的文件夹路径: ").strip()
    
    # 设置输出Excel文件名
    output_filename = "file_nas.xlsx"
    
    # 获取文件结构
    structure = get_file_structure(folder_path)
    
    # 写入Excel
    write_to_excel(structure, output_filename)